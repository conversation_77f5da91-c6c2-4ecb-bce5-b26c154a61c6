"use client";

import React, { useEffect, useState } from "react";
import { UseGetExperience } from "../experience/queries/use-get-experience";
import { UseUpdateExperience } from "../experience/mutations/use-update-experience";
import { toast } from "sonner";
import Link from "next/link";
import { Pen, Trash } from "lucide-react";
import { Button } from "@/components/ui/button";

const DescriptionSection: React.FC = () => {
  const { data, isLoading, error } = UseGetExperience();
  const updateExperience = UseUpdateExperience();
  const [experienceBlock, setExperienceBlock] = useState(data?.data || null);

  useEffect(() => {
    if (data?.data) {
      setExperienceBlock(data.data);
    }
  }, [data]);

  const handleDeleteFeature = (featureId: string) => {
    if (!experienceBlock) return;
    const confirmDelete = window.confirm("Are you sure you want to delete this feature?");
    if (!confirmDelete) return;

    const updatedFeatures = experienceBlock.features.filter(feature => feature.id !== featureId);
    const updatedExperience = { ...experienceBlock, features: updatedFeatures };

    updateExperience.mutate(updatedExperience, {
      onSuccess: () => {
        toast.success("Feature deleted successfully.");
        setExperienceBlock(updatedExperience); // Update local state for UI
      },
      onError: (err) => {
        toast.error(`Delete failed: ${err.message}`);
      }
    });
  };

  if (isLoading) return <p>Loading experience...</p>;
  if (error) return <p>Error loading experience: {error.message}</p>;

  if (!experienceBlock) {
    return (
      <div className="p-6 max-w-full mx-auto">
        <p className="mb-4 text-lg font-medium">No experience data found.</p>
        <button
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
          onClick={() => window.location.href = "/home/<USER>/create"}
        >
          Create Experience
        </button>
      </div>
    );
  }

  return (
    <div>
      <section className="p-6">
        <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold">{experienceBlock.heading}</h1>
            <Button
              onClick={() => window.location.href = "/home/<USER>/edit"}
            >
              Edit Experience
            </Button>
          </div>
          {experienceBlock.subHeading && (
            <p className="mb-6">{experienceBlock.subHeading}</p>
          )}
          <div className="bg-white rounded-lg shadow">
            <table className="min-w-full text-left border-separate border-spacing-0">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">SN</th>
                  <th className="border px-4 py-2">Feature Title</th>
                  <th className="border px-4 py-2">Feature Subtitle</th>
                  <th className="border px-4 py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {experienceBlock.features.map((feature, idx) => (
                  <tr key={idx} className="even:bg-gray-50">
                    <td className="border px-4 py-2">{idx + 1}</td>
                    <td className="border px-4 py-2">{feature.title}</td>
                    <td className="border px-4 py-2">{feature.subtitle}</td>
                    <td className="py-2 px-4 flex gap-2">
                      <div className="flex space-x-2">
                        <Link
                          href={`/home/<USER>/edit`}
                          className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                        >
                          <Pen className="w-4 h-4" />
                        </Link>
                        <button
                          onClick={() => handleDeleteFeature(feature.id)}
                          className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                          type="button"
                        >
                          <Trash className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DescriptionSection;
