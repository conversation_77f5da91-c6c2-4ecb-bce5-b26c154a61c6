'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePara<PERSON> } from 'next/navigation'
import { useUpdatePackage } from '@/modules/package/mutations/update-package'
import { useGetActivity } from '@/modules/activity/queries/use-get-activity'
import { useGetRegion } from '@/modules/region/queries/use-get-region'
import { IPackage, IPackageHighlights, IPackageDescription, IPackageShortItinerary, IPackageInclusions, IPackageExclusions, IPackageGallery, IPackageYtVideo, IPackageMap, IPackageInfo } from '@/types/package_'
import { Button } from '@/components/ui/button'
import { useGetPackageById } from '@/modules/package/queries/get-package-by-id'
import { PackageDetailsForm } from '@/modules/product/component/package-detail-form'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { PackageHighlightsForm } from '@/modules/product/component/package-detail/highlight-section'
import { PackageDescriptionForm } from '@/modules/product/component/package-detail/description-section'
import { PackageShortItineraryForm } from '@/modules/product/component/package-detail/short-itinerary-section'
import { PackageInclusionsExclusionsForm } from '@/modules/product/component/package-detail/include-section'
import { PackageGalleryVideoForm } from '@/modules/product/component/package-detail/photo-section'
import { PackageVideoForm } from '@/modules/product/component/package-detail/review-video'
import { PackageMapForm } from '@/modules/product/component/package-detail/map-section'
import { PackageTripInfoForm } from '@/modules/product/component/package-detail/trip-info-section'
import { ZodError } from 'zod'
import { packageSchema } from '@/modules/package/schema/edit-package-schema'


export default function EditPackagePage() {
  const router = useRouter()
  const { id } = useParams() as { id: string }

  const { data: packageResponse, isLoading, isError } = useGetPackageById(id)
  const { data: activityData, isLoading: activityLoading } = useGetActivity()
  const { data: regionData, isLoading: regionLoading } = useGetRegion()

  const updatePackageMutation = useUpdatePackage(id)

  const [packageData, setPackageData] = useState<Partial<IPackage>>({})
  const [highlights, setHighlights] = useState<Partial<IPackageHighlights>>({})
  const [description, setDescription] = useState<Partial<IPackageDescription>>({})
  const [shortItinerary, setShortItinerary] = useState<Partial<IPackageShortItinerary>>({})
  const [inclusions, setInclusions] = useState<Partial<IPackageInclusions>>({})
  const [exclusions, setExclusions] = useState<Partial<IPackageExclusions>>({})
  const [gallery, setGallery] = useState<Partial<IPackageGallery>>({})
  const [ytVideo, setYtVideo] = useState<Partial<IPackageYtVideo>>({})
  const [map, setMap] = useState<Partial<IPackageMap>>({})
  const [info, setInfo] = useState<Partial<IPackageInfo>>({})

  useEffect(() => {
    if (packageResponse?.data) {
      const pkg = packageResponse.data
      setPackageData(pkg)
      setHighlights(pkg.highlights || {})
      setDescription(pkg.description || {})
      setShortItinerary(pkg.shortItinerary || {})
      setInclusions(pkg.inclusions || {})
      setExclusions(pkg.exclusions || {})
      setGallery(pkg.gallery || {})
      setYtVideo(pkg.ytVideo || {})
      setMap(pkg.map || {})
      setInfo(pkg.info || {})
    }
  }, [packageResponse])

  if (isLoading || activityLoading || regionLoading) return <div>Loading...</div>
  if (isError) return <div>Error loading package details.</div>
  if (!packageData) return <div>Loading package data...</div>

  const handleSave = () => {
    if (!packageData) return

    const packageToUpdate: IPackage = {
      ...packageData,
      id: packageData.id || id,
      highlights,
      description,
      shortItinerary,
      inclusions,
      exclusions,
      gallery,
      ytVideo,
      map,
      info,
    } as IPackage

    console.log('Payload being sent:', JSON.stringify(packageToUpdate, null, 2))

    try {
      packageSchema.parse(packageToUpdate);

      updatePackageMutation.mutate(packageToUpdate, {
        onSuccess: () => {
          alert('Package updated successfully');
          router.push('/package');
        },
        onError: (error: Error) => {
          alert(`Error updating package: ${error.message}`);
        },
      });
    } catch (err) {
      if (err instanceof ZodError) {
        err.issues.forEach(e => alert(e.message));
      } else {
        alert("Unexpected validation error");
      }
    }
  };

  return (
    <div className="w-screen max-w-full overflow-x-hidden">
      <div className="w-full max-w-full p-6 bg-gray-50">
        <h1 className="px-4 text-3xl font-bold mb-4">
          Edit &quot;{packageData.name}&quot; Package Details
        </h1>
        <div className="w-full max-w-full">
          <EditTabs packageId={id} />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageDetailsForm
            formData={packageData}
            onFormDataChange={setPackageData}
            activities={activityData?.data || []}
            regions={regionData?.data || []}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageHighlightsForm
            highlights={highlights}
            onHighlightsChange={setHighlights}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageDescriptionForm
            description={description}
            onDescriptionChange={setDescription}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageShortItineraryForm
            shortItinerary={shortItinerary}
            onShortItineraryChange={setShortItinerary}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageInclusionsExclusionsForm
            inclusions={inclusions}
            exclusions={exclusions}
            onInclusionsChange={setInclusions}
            onExclusionsChange={setExclusions}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageGalleryVideoForm
            gallery={gallery}
            ytVideo={ytVideo}
            onGalleryChange={setGallery}
            onYtVideoChange={setYtVideo}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageVideoForm
            ytVideo={ytVideo}
            onYtVideoChange={setYtVideo}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageMapForm
            map={map}
            onMapChange={setMap}
          />
        </div>

        <div className="w-full max-w-full mb-5">
          <PackageTripInfoForm
            info={info}
            onInfoChange={setInfo}
          />
        </div>

        <div className="mt-6 flex justify-end w-full max-w-full">
          <Button
            onClick={handleSave}
            className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            disabled={updatePackageMutation.isPending}
          >
            {updatePackageMutation.isPending ? 'Updating...' : 'Update Package'}
          </Button>
        </div>
      </div>
    </div>
  )
}
