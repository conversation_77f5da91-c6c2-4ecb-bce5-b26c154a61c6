"use client"

import HeroSection from '@/modules/home/<USER>/hero-section'
import React from 'react'
import ExperienceDisplay from '@/modules/home/<USER>/experience-section'
import HikingAreaSection from '@/modules/home/<USER>/hiking-area-section'
import TailoredAdventureSection from '@/modules/home/<USER>/tailored-adventure-section'
import ReviewSection from '@/modules/home/<USER>/review-section'
import VideoTestimonialSection from '@/modules/home/<USER>/video-testimonial-section'
import HomeSEOSection from '@/modules/home/<USER>/template/seo-section'
import { useGetHomeSEO } from '@/modules/home/<USER>/queries/get-home-seo'
import DescriptionSection from '@/modules/home/<USER>/description-section'

const HomeSectionPage = () => {

  const { data, isLoading, error } = useGetHomeSEO();

  if (isLoading) return <div>Loading SEO data...</div>;
  if (error) return <div>Error loading SEO data.</div>;

  return (
    <div className='bg-gray-100'>
      <HeroSection />
      <DescriptionSection/>
      <ExperienceDisplay />
      <HikingAreaSection />
      <TailoredAdventureSection />
      <ReviewSection />
      <VideoTestimonialSection />
      {data?.data && <HomeSEOSection seoData={data.data} />}
    </div>
  )
}

export default HomeSectionPage